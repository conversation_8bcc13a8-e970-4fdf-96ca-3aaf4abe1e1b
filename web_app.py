#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web interface cho EmailChatbot
"""

from flask import Flask, render_template, request, jsonify, session, redirect
from flask_cors import CORS
import uuid
import json
from datetime import datetime
import os
from werkzeug.utils import secure_filename
from pathlib import Path
import pdfplumber

# Import chatbot components
from main import EmailChatbot
from intent_detector import IntentDetector
from gemini_ai import <PERSON><PERSON><PERSON>
from config import COMPANY_INFO
from cv_analyzer import <PERSON><PERSON><PERSON>yzer
from new_response_generator import NewResponseGenerator

# Import vector chatbot components
from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
from vector_chatbot.new_response_generator_vector import NewResponseGeneratorVector

app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'your-secret-key-here')
CORS(app)

# Configure upload settings
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024  # 10MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'txt', 'jpg', 'jpeg', 'png'}

# Create upload directory
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize components
chatbot = EmailChatbot()

# Initialize CV analyzer with lazy loading
cv_analyzer = None


def get_cv_analyzer():
    """Lazy initialization of CV analyzer"""
    global cv_analyzer
    if cv_analyzer is None:
        try:
            cv_analyzer = CVAnalyzer()
        except Exception as e:
            print(f"⚠️ CV analyzer initialization failed: {e}")
            cv_analyzer = False  # Mark as failed
    return cv_analyzer if cv_analyzer is not False else None


# Initialize new intent system
gemini_ai = GeminiAI()
new_response_gen = NewResponseGenerator(gemini_ai)

# Initialize vector chatbot system (lazy initialization to avoid startup delays)
vector_chatbot_router = None
vector_response_gen = None


def get_vector_chatbot_router():
    """Lazy initialization of vector chatbot router"""
    global vector_chatbot_router
    if vector_chatbot_router is None:
        try:
            print("🔄 Lazy initializing vector chatbot system...")
            vector_chatbot_router = VectorChatbotRouter()
            print("✅ Vector chatbot system initialized successfully")
        except Exception as e:
            print(f"⚠️ Vector chatbot system initialization failed: {e}")
            vector_chatbot_router = False  # Mark as failed to avoid retrying
    return vector_chatbot_router if vector_chatbot_router is not False else None


def get_vector_response_generator():
    """Lazy initialization of vector response generator"""
    global vector_response_gen
    if vector_response_gen is None:
        try:
            vector_response_gen = NewResponseGeneratorVector(gemini_ai)
            print("✅ Vector response generator initialized successfully")
        except Exception as e:
            print(f"⚠️ Vector response generator initialization failed: {e}")
            vector_response_gen = False  # Mark as failed to avoid retrying
    return vector_response_gen if vector_response_gen is not False else None


# Store conversations and CV data in memory (in production, use database)
conversations = {}
cv_analyses = {}


def allowed_file(filename):
    """Kiểm tra file extension có được phép không"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route('/')
def index():
    """Trang chủ chatbot - redirect to modern interface"""
    # Get language from URL parameter or default to vi_VN
    language = request.args.get('language', 'vi_VN')
    return redirect(f'/modern?language={language}')


@app.route('/simple')
def simple():
    """Trang chatbot đơn giản để debug"""
    return render_template('simple.html', company_info=COMPANY_INFO)


@app.route('/debug')
def debug():
    """Debug information"""
    import os
    debug_info = {
        'server_status': 'running',
        'chatbot_available': True,
        'static_files': {
            'css': os.path.exists('static/css/style.css'),
            'js': os.path.exists('static/js/app.js')
        },
        'templates': {
            'index': os.path.exists('templates/index.html'),
            'simple': os.path.exists('templates/simple.html')
        },
        'company_info': COMPANY_INFO['NAME'],
        'endpoints': [
            '/ - Main chatbot',
            '/simple - Simple version',
            '/debug - This page',
            '/health - Health check',
            '/api/chat - Chat API',
            '/api/chat-vector - Vector-based Chat API',
            '/api/vector-analysis - Vector Intent Analysis',
            '/api/suggestions - Suggestions',
            '/api/company-info - Company info'
        ]
    }

    return jsonify(debug_info)


@app.route('/test')
def test():
    """Minimal test page"""
    return render_template('test.html')


@app.route('/modern')
def modern():
    """Modern chatbot interface"""
    # Get language from URL parameter
    language = request.args.get('language', 'vi')

    # Map language codes
    language_map = {
        'vi_VN': 'vi',
        'en_US': 'en',
        'ja_JP': 'ja',
        'vi': 'vi',
        'en': 'en',
        'ja': 'ja'
    }

    # Normalize language code
    normalized_lang = language_map.get(language, 'vi')

    return render_template('modern.html',
                           company_info=COMPANY_INFO,
                           initial_language=normalized_lang,
                           initial_theme='dark')


# Helper routes for specific languages
@app.route('/vi')
def vietnamese():
    """Vietnamese interface"""
    return redirect('/modern?language=vi_VN')


@app.route('/en')
def english():
    """English interface"""
    return redirect('/modern?language=en_US')


@app.route('/ja')
def japanese():
    """Japanese interface"""
    return redirect('/modern?language=ja_JP')


@app.route('/api/chat', methods=['POST'])
def chat():
    """API endpoint cho chat với callback support"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        language = data.get('language', 'vi')  # Get language from request

        # print(f"🌍 Chat API received language: {language}")  # Debug log

        if not message:
            return jsonify({
                'error': 'Message không được để trống'
            }), 400

        # Tạo hoặc lấy session ID
        if 'user_id' not in session:
            session['user_id'] = str(uuid.uuid4())

        user_id = session['user_id']

        # Tạo conversation history nếu chưa có
        if user_id not in conversations:
            conversations[user_id] = []

        # Lưu message của user
        conversations[user_id].append({
            'type': 'user',
            'message': message,
            'timestamp': datetime.now().isoformat()
        })

        # Get conversation history for context (structured data with type and message)
        conversation_history = []
        if user_id in conversations:
            # Pass the full conversation history with type information
            conversation_history = conversations[user_id].copy()

        # Auto-detect language if not provided

        # First, generate thinking message to check if callback is needed
        thinking_data = new_response_gen.generate_thinking_message(
            user_id, message, conversation_history)

        # If callback is needed, return thinking message with callback flag
        if thinking_data.get('needs_callback', False):
            return jsonify({
                'thinking_message': thinking_data['thinking_message'],
                'user_intent': thinking_data['user_intent'],
                'needs_callback': True,
                'callback_url': '/api/chat/callback',
                'user_id': user_id,
                'timestamp': datetime.now().isoformat()
            })

        # If no callback needed, generate full response
        response_data = new_response_gen.generate_response(
            user_id, message, conversation_history)

        # Lưu message của bot
        conversations[user_id].append({
            'type': 'bot',
            'message': response_data['message'],
            'timestamp': datetime.now().isoformat()
        })

        return jsonify({
            'contextual_followup': response_data['contextual_followup'],
            'response_type': response_data['response_type'],
            'tone': response_data['tone'],
            'emotion': response_data['emotion'],
            'user_intent': response_data['user_intent'],
            'thinking_message': response_data['thinking_message'],
            'response': response_data['message'],
            'suggest_questions': response_data['suggestion_answers'],
            'needs_callback': response_data.get('needs_callback', False),
            'token_usage': response_data.get('token_usage', {}),
            'user_id': user_id,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Error in chat endpoint: {str(e)}")
        return jsonify({
            'error': 'Có lỗi xảy ra khi xử lý tin nhắn'
        }), 500


@app.route('/api/chat-vector', methods=['POST'])
def chat_vector():
    """API endpoint cho vector-based chat với 6-step process"""
    try:
        # Get vector system with lazy initialization
        router = get_vector_chatbot_router()
        if not router:
            return jsonify({
                'error': 'Vector chatbot system không khả dụng',
                'fallback_to_regular': True
            }), 503

        data = request.get_json()
        message = data.get('message', '').strip()
        language = data.get('language', 'vi')

        if not message:
            return jsonify({
                'error': 'Message không được để trống'
            }), 400

        # Tạo hoặc lấy session ID
        if 'user_id' not in session:
            session['user_id'] = str(uuid.uuid4())

        user_id = session['user_id']

        # Tạo conversation history nếu chưa có
        if user_id not in conversations:
            conversations[user_id] = []

        # Lưu message của user
        conversations[user_id].append({
            'type': 'user',
            'message': message,
            'timestamp': datetime.now().isoformat()
        })

        # Get conversation history for context
        conversation_history = []
        if user_id in conversations:
            conversation_history = conversations[user_id].copy()

        # Process using vector-based 6-step system
        print(f"🎯 Processing with Vector Chatbot: '{message}'")
        response_data = router.process_user_input(
            message, user_id, conversation_history
        )

        # Lưu message của bot
        conversations[user_id].append({
            'type': 'bot',
            'message': response_data['message'],
            'timestamp': datetime.now().isoformat()
        })

        return jsonify({
            'response': response_data['message'],
            'response_type': response_data.get('response_type', 'text'),
            'tone': response_data.get('tone', 'friendly'),
            'emotion': response_data.get('emotion', 'helpful'),
            'user_intent': response_data.get('user_intent', 'unknown'),
            'thinking_message': response_data.get('thinking_message', ''),
            'suggest_questions': response_data.get('suggestion_answers', []),
            'contextual_followup': response_data.get('contextual_followup', {}),
            'token_usage': response_data.get('token_usage', {}),

            # Vector-specific metadata
            'vector_intent': response_data.get('vector_intent', 'unknown'),
            'vector_confidence': response_data.get('vector_confidence', 0.0),
            'mapped_intent': response_data.get('mapped_intent', 'unknown'),
            'processing_method': response_data.get('processing_method', 'unknown'),
            'vector_routing': response_data.get('vector_routing', False),
            'intent_similarities': response_data.get('intent_similarities', {}),

            'user_id': user_id,
            'timestamp': datetime.now().isoformat(),
            'api_version': 'vector-1.0'
        })

    except Exception as e:
        print(f"❌ Error in vector chat endpoint: {str(e)}")
        return jsonify({
            'error': 'Có lỗi xảy ra khi xử lý tin nhắn với vector system',
            'fallback_available': True
        }), 500


@app.route('/api/vector-analysis', methods=['POST'])
def vector_analysis():
    """API endpoint để phân tích vector intent cho debugging"""
    try:
        router = get_vector_chatbot_router()
        if not router:
            return jsonify({
                'error': 'Vector system không khả dụng'
            }), 503

        data = request.get_json()
        message = data.get('message', '').strip()

        if not message:
            return jsonify({
                'error': 'Message không được để trống'
            }), 400

        # Get detailed vector analysis
        analysis = router.get_intent_analysis(message)

        return jsonify({
            'success': True,
            'analysis': analysis,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Error in vector analysis endpoint: {str(e)}")
        return jsonify({
            'error': 'Có lỗi xảy ra khi phân tích vector'
        }), 500


@app.route('/api/chat/callback', methods=['POST'])
def chat_callback():
    """API endpoint cho callback sau thinking message"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        user_intent = data.get('user_intent')
        language = data.get('language', 'vi')  # Get language from request

        if not user_id:
            return jsonify({
                'error': 'User ID không được để trống'
            }), 400

        # Get conversation history (structured data)
        conversation_history = []
        if user_id in conversations:
            conversation_history = conversations[user_id].copy()

        # Get the last user message
        last_message = ""
        if conversation_history:
            # Find the last user message
            for msg in reversed(conversation_history):
                if msg.get('type') == 'user':
                    last_message = msg.get('message', '')
                    break

        # Generate detailed response based on intent
        if user_intent in ['search_jobs', 'filter_jobs', 'salary_query', 'ask_company_info', 'cv_feedback', 'upload_resume', 'update_resume', 'job_it_trending']:
            response_data = new_response_gen.generate_thinking_message_response(
                user_id, last_message, conversation_history, user_intent)
        else:
            # Fallback to regular response
            response_data = new_response_gen.generate_response(
                user_id, last_message, conversation_history)

        # Save bot response to conversation history
        conversations[user_id].append({
            'type': 'bot',
            'message': response_data['message'],
            'timestamp': datetime.now().isoformat()
        })

        return jsonify({
            'response': response_data['message'],
            'response_type': response_data.get('response_type', 'text'),
            'tone': response_data.get('tone', 'friendly'),
            'emotion': response_data.get('emotion', 'helpful'),
            'suggest_questions': response_data['suggestion_answers'],
            'contextual_followup': response_data['contextual_followup'],
            'token_usage': response_data.get('token_usage', {}),
            'user_id': user_id,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Error in callback endpoint: {str(e)}")
        return jsonify({
            'error': 'Có lỗi xảy ra khi xử lý callback'
        }), 500


def get_upload_messages(language='vi'):
    """Get localized messages for file upload"""
    messages = {
        'vi': {
            'no_file': 'Không có file được upload',
            'no_file_selected': 'Không có file được chọn',
            'pdf_only': 'Chỉ hỗ trợ file PDF',
            'file_too_large': 'File quá lớn. Tối đa 10MB',
            'no_content': 'Không thể đọc nội dung từ file PDF này',
            'content_truncated': '[Nội dung đã được cắt ngắn...]',
            'page_prefix': 'Trang',
            'success_message': 'Đã đọc thành công file PDF "{filename}". Nội dung sẽ được gửi đến chatbot.',
            'pdf_error': 'Lỗi khi xử lý file PDF: {error}',
            'upload_error': 'Có lỗi xảy ra khi upload file'
        },
        'en': {
            'no_file': 'No file uploaded',
            'no_file_selected': 'No file selected',
            'pdf_only': 'Only PDF files are supported',
            'file_too_large': 'File too large. Maximum 10MB',
            'no_content': 'Cannot read content from this PDF file',
            'content_truncated': '[Content has been truncated...]',
            'page_prefix': 'Page',
            'success_message': 'Successfully read PDF file "{filename}". Content will be sent to chatbot.',
            'pdf_error': 'Error processing PDF file: {error}',
            'upload_error': 'An error occurred while uploading file'
        },
        'ja': {
            'no_file': 'ファイルがアップロードされていません',
            'no_file_selected': 'ファイルが選択されていません',
            'pdf_only': 'PDFファイルのみサポートされています',
            'file_too_large': 'ファイルが大きすぎます。最大10MB',
            'no_content': 'このPDFファイルからコンテンツを読み取れません',
            'content_truncated': '[コンテンツが切り詰められました...]',
            'page_prefix': 'ページ',
            'success_message': 'PDFファイル"{filename}"の読み取りに成功しました。コンテンツはチャットボットに送信されます。',
            'pdf_error': 'PDFファイル処理エラー: {error}',
            'upload_error': 'ファイルアップロード中にエラーが発生しました'
        }
    }
    return messages.get(language, messages['vi'])


@app.route('/api/upload-pdf', methods=['POST'])
def upload_pdf():
    """API endpoint để upload và xử lý file PDF"""
    try:
        # Get language from request
        language = request.form.get('language', 'vi')
        if language.startswith('vi'):
            language = 'vi'
        elif language.startswith('en'):
            language = 'en'
        elif language.startswith('ja'):
            language = 'ja'
        else:
            language = 'vi'

        messages = get_upload_messages(language)
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({
                'error': messages['no_file']
            }), 400

        file = request.files['file']

        # Check if file is selected
        if file.filename == '':
            return jsonify({
                'error': messages['no_file_selected']
            }), 400

        # Check file extension
        if not file.filename.lower().endswith('.pdf'):
            return jsonify({
                'error': messages['pdf_only']
            }), 400

        # Check file size (already handled by Flask config, but double check)
        file.seek(0, 2)  # Seek to end
        file_size = file.tell()
        file.seek(0)  # Reset to beginning

        if file_size > 10 * 1024 * 1024:  # 10MB
            return jsonify({
                'error': messages['file_too_large']
            }), 400

        # Extract text from PDF
        try:
            text_content = ""
            with pdfplumber.open(file) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    page_text = page.extract_text()
                    if page_text:
                        text_content += f"\n--- {messages['page_prefix']} {page_num} ---\n"
                        text_content += page_text.strip()
                        text_content += "\n"

            # Clean up the text
            text_content = text_content.strip()

            if not text_content:
                return jsonify({
                    'error': messages['no_content']
                }), 400

            # Limit text length to prevent overwhelming the AI
            max_length = 5000  # Adjust as needed
            if len(text_content) > max_length:
                text_content = text_content[:max_length] + \
                    f"\n\n{messages['content_truncated']}"

            return jsonify({
                'success': True,
                'text_content': text_content,
                'filename': secure_filename(file.filename),
                'file_size': file_size,
                'message': messages['success_message'].format(filename=file.filename)
            })

        except Exception as pdf_error:
            print(f"❌ PDF processing error: {str(pdf_error)}")
            return jsonify({
                'error': messages['pdf_error'].format(error=str(pdf_error))
            }), 500

    except Exception as e:
        print(f"❌ Upload error: {str(e)}")
        return jsonify({
            'error': messages['upload_error']
        }), 500

        # # Lưu response của bot
        # conversations[user_id].append({
        #     'type': 'bot',
        #     'message': response,
        #     'timestamp': datetime.now().isoformat()
        # })

        # return jsonify({
        #     'response': response,
        #     'suggest_questions': suggest_questions,
        #     'user_id': user_id,
        #     'timestamp': datetime.now().isoformat(),
        #     'intent_analysis': {
        #         'customer_type': intent_analysis.customer_type.value,
        #         'specific_intent': intent_analysis.specific_intent.value if hasattr(intent_analysis.specific_intent, 'value') else str(intent_analysis.specific_intent),
        #         'confidence': intent_analysis.confidence,
        #         'reasoning': intent_analysis.reasoning
        #     }
        # })

    except Exception as e:
        print(f"Error in chat endpoint: {str(e)}")
        return jsonify({
            'error': 'Có lỗi xảy ra, vui lòng thử lại'
        }), 500


@app.route('/api/conversation', methods=['GET'])
def get_conversation():
    """Lấy lịch sử conversation"""
    try:
        if 'user_id' not in session:
            return jsonify({'conversation': []})

        user_id = session['user_id']
        conversation = conversations.get(user_id, [])

        return jsonify({
            'conversation': conversation,
            'user_id': user_id
        })

    except Exception as e:
        print(f"Error getting conversation: {str(e)}")
        return jsonify({'error': 'Không thể lấy lịch sử chat'}), 500


@app.route('/api/clear', methods=['POST'])
def clear_conversation():
    """Xóa lịch sử conversation"""
    try:
        if 'user_id' in session:
            user_id = session['user_id']
            if user_id in conversations:
                conversations[user_id] = []

        return jsonify({'success': True})

    except Exception as e:
        print(f"Error clearing conversation: {str(e)}")
        return jsonify({'error': 'Không thể xóa lịch sử chat'}), 500


@app.route('/api/suggestions', methods=['GET'])
def get_suggestions():
    """Lấy gợi ý câu hỏi theo ngôn ngữ"""
    lang = request.args.get('lang', 'vi')

    suggestions_by_lang = {
        'vi': [
            "🔥 Thị trường IT Việt Nam 2024-2025 như thế nào?",
            "🚀 Công nghệ nào đang hot nhất hiện tại?",
            "💰 Mức lương AI/ML Engineer bao nhiêu?",
            "🐍 Python Developer có cơ hội như thế nào?",
            "☁️ Cloud Engineer có nhu cầu cao không?",
            "🔗 Blockchain Developer lương bao nhiêu?",
            "💼 Freelance IT có thu nhập cao không?",
            "📈 Dự báo thị trường IT 2025?",
            "Tôi muốn tìm hiểu về FOIS ICT PRO",
            "Tôi muốn gửi CV",
            "Có job remote nào không?",
            "Quy trình ứng tuyển như thế nào?"
        ],
        'en': [
            "🔥 What is the IT market in Vietnam like in 2024–2025?",
            "🚀 Which technology is the hottest right now?",
            "💰 What is the salary range for AI/ML Engineers?",
            "🐍 What opportunities are there for Python Developers?",
            "☁️ Is there high demand for Cloud Engineers?",
            "🔗 How much do Blockchain Developers earn?",
            "💼 Do IT freelancers have high incomes?",
            "📈 What is the forecast for the IT market in 2025?",
            "I want to learn about FOIS ICT PRO",
            "I want to submit my CV",
            "Are there any remote jobs available?",
            "What is the application process?"
        ],
        'ja': [
            "🔥 2024～2025年のベトナムのIT市場はどのようになっていますか？",
            "🚀 今最も注目されている技術は何ですか？",
            "💰 AI/MLエンジニアの給与レンジはいくらですか？",
            "🐍 Python開発者にはどのようなチャンスがありますか？",
            "☁️ クラウドエンジニアの需要は高いですか？",
            "🔗 ブロックチェーン開発者の給与はいくらですか？",
            "💼 ITフリーランスの収入は高いですか？",
            "📈 2025年のIT市場予測は？",
            "FOIS GROUPについて知りたいです",
            "履歴書を提出したいです",
            "リモートワークの求人はありますか？",
            "応募プロセスはどのようになりますか？"
        ],
    }

    suggestions = suggestions_by_lang.get(lang, suggestions_by_lang['vi'])
    return jsonify({'suggestions': suggestions})


@app.route('/api/company-info', methods=['GET'])
def get_company_info():
    """Lấy thông tin công ty"""
    return jsonify({
        'company': COMPANY_INFO
    })


@app.route('/api/market-insights', methods=['GET'])
def get_market_insights():
    """Lấy thông tin thị trường IT trending từ báo cáo 2024-2025"""
    try:
        # Get market insights summary
        market_data = new_response_gen.get_market_insights_summary()

        return jsonify({
            'success': True,
            'market_insights': market_data,
            'data_source': 'ITviec, TopDev, Navigos Reports 2024-2025',
            'last_updated': '2024-2025',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Error getting market insights: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Không thể lấy thông tin thị trường'
        }), 500


@app.route('/api/trending-jobs', methods=['POST'])
def get_trending_jobs():
    """API endpoint cho câu hỏi về trending jobs và market insights"""
    try:
        data = request.get_json()
        query = data.get(
            'query', 'Thị trường IT Việt Nam hiện tại như thế nào?')

        # Tạo hoặc lấy session ID
        if 'user_id' not in session:
            session['user_id'] = str(uuid.uuid4())

        user_id = session['user_id']

        # Get conversation history (structured data)
        conversation_history = []
        if user_id in conversations:
            conversation_history = conversations[user_id].copy()

        # Generate trending response using the regular response generator with job_it_trending intent
        response_data = new_response_gen.generate_thinking_message_response(
            user_id, query, conversation_history, 'job_it_trending')

        # Store in conversation history
        if user_id not in conversations:
            conversations[user_id] = []

        return jsonify({
            'success': True,
            'response': response_data['message'],
            'response_type': response_data.get('response_type', 'market_insights'),
            'tone': response_data.get('tone', 'professional'),
            'emotion': response_data.get('emotion', 'enthusiastic'),
            'suggest_questions': response_data.get('suggestion_answers', []),
            'contextual_followup': response_data.get('contextual_followup', {}),
            'market_data': response_data.get('market_data', {}),
            'token_usage': response_data.get('token_usage', {}),
            'data_source': 'ITviec, TopDev, Navigos Reports 2024-2025',
            'user_id': user_id,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Error in trending jobs endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Có lỗi xảy ra khi xử lý câu hỏi về thị trường IT'
        }), 500


@app.route('/api/upload-cv', methods=['POST'])
def upload_cv():
    """Upload và phân tích CV"""
    try:
        # Kiểm tra có file trong request không
        if 'cv_file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'Không tìm thấy file CV'
            }), 400

        file = request.files['cv_file']

        # Kiểm tra file có được chọn không
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'Chưa chọn file'
            }), 400

        # Kiểm tra định dạng file
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': f'Định dạng file không hỗ trợ. Chỉ chấp nhận: {", ".join(ALLOWED_EXTENSIONS)}'
            }), 400

        # Tạo tên file an toàn
        filename = secure_filename(file.filename)
        user_id = session.get('user_id', str(uuid.uuid4()))
        session['user_id'] = user_id

        # Tạo tên file unique
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_filename = f"{user_id}_{timestamp}_{filename}"

        # Lưu file
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)

        # Get CV analyzer and check if available
        analyzer = get_cv_analyzer()
        if not analyzer:
            return jsonify({
                'success': False,
                'error': 'CV processing không khả dụng. Vui lòng cài đặt thư viện cần thiết.'
            }), 503

        # Phân tích CV
        analysis_result = analyzer.process_cv_file(file_path)

        # Lưu kết quả phân tích
        cv_analyses[user_id] = {
            'filename': filename,
            'upload_time': datetime.now().isoformat(),
            'analysis': analysis_result
        }

        # Xóa file sau khi phân tích (để tiết kiệm dung lượng)
        try:
            os.remove(file_path)
        except:
            pass

        if analysis_result['success']:
            return jsonify({
                'success': True,
                'message': 'CV đã được phân tích thành công!',
                'cv_data': analysis_result['cv_data'],
                'job_matches': analysis_result['job_matches'],
                'recommendations': analysis_result['recommendations'],
                'total_jobs_found': analysis_result['total_jobs_found']
            })
        else:
            return jsonify({
                'success': False,
                'error': analysis_result['error']
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Lỗi xử lý file: {str(e)}'
        }), 500


@app.route('/api/cv-analysis')
def get_cv_analysis():
    """Lấy kết quả phân tích CV của user"""
    user_id = session.get('user_id')

    if not user_id or user_id not in cv_analyses:
        return jsonify({
            'success': False,
            'error': 'Chưa có CV nào được phân tích'
        }), 404

    analysis_data = cv_analyses[user_id]
    return jsonify({
        'success': True,
        'filename': analysis_data['filename'],
        'upload_time': analysis_data['upload_time'],
        'analysis': analysis_data['analysis']
    })


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Test chatbot availability
        test_response = chatbot.generate_response(
            "test",
            "health_check",
            "Health Check"
        )

        return jsonify({
            'status': 'healthy',
            'chatbot': 'available',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


if __name__ == '__main__':
    import argparse
    import os

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='FOIS Chatbot Web Interface')
    parser.add_argument('--host', default='0.0.0.0',
                        help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5001,
                        help='Port to bind to (default: 5001)')
    parser.add_argument('--debug', action='store_true',
                        default=True, help='Enable debug mode')
    parser.add_argument('--no-reload', action='store_true',
                        help='Disable auto-reload to prevent double initialization')
    args = parser.parse_args()

    # Check if we're in the main process (not Flask's reloader subprocess)
    is_main_process = os.environ.get('WERKZEUG_RUN_MAIN') != 'true'

    if is_main_process:
        print("🚀 Starting FOIS Chatbot Web Interface...")
        print(f"🏢 Company: {COMPANY_INFO['FULL_NAME']}")
        print(f"🌐 Access at: http://localhost:{args.port}")
        print(f"🔧 Debug mode: {'ON' if args.debug else 'OFF'}")
        print(f"🎯 New Intent System: ACTIVE")
        print(f"🔄 Auto-reload: {'OFF' if args.no_reload else 'ON'}")
        if not args.no_reload and args.debug:
            print("⚠️  Note: Debug mode will restart server once (this is normal)")
        print("=" * 50)

    # Development mode
    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug,
        use_reloader=not args.no_reload,  # Allow disabling reloader
        threaded=True
    )
