// Internationalization (i18n) for FOIS Chatbot
class I18n {
    constructor() {
        // Priority: URL parameter > localStorage > default
        const urlLanguage = window.INITIAL_LANGUAGE;
        const storedLanguage = localStorage.getItem('chatbot-language');

        this.currentLanguage = urlLanguage || storedLanguage || 'vi';

        // Update localStorage with current language
        localStorage.setItem('chatbot-language', this.currentLanguage);

        this.translations = {};
        this.loadTranslations();

        console.log(`🌍 I18n initialized with language: ${this.currentLanguage} (from ${urlLanguage ? 'URL' : storedLanguage ? 'localStorage' : 'default'})`);
    }

    loadTranslations() {
        this.translations = {
            // Vietnamese (Default)
            'vi': {
                // Header
                'header.title': 'FOIS ICT PRO AI Assistant',
                'header.status': 'Đang hoạt động',
                'header.theme_toggle': 'Chuyển đổi giao diện',
                
                // Sidebar
                'sidebar.company_experience': 'kinh nghiệm',
                'sidebar.location': 'Nhật Bản & Việt Nam',
                'sidebar.mission': 'Tạo ra giải pháp công nghệ tối ưu cho doanh nghiệp...',
                'sidebar.clear_history': 'Xóa lịch sử',
                'sidebar.language': 'Ngôn ngữ',
                
                // Quick Actions
                'actions.company': 'Thông tin công ty',
                'actions.jobs': 'Tìm việc làm',
                'actions.salary': 'Mức lương',
                'actions.guide': 'Hướng dẫn',
                'actions.upload_cv': 'Upload CV',
                
                // Chat Interface
                'chat.placeholder': 'Nhập tin nhắn của bạn...',
                'chat.send': 'Gửi',
                'chat.attach': 'Đính kèm file',
                'chat.typing': 'Đang nhập...',
                'chat.suggestions_title': 'Gợi ý câu hỏi',

                // Buttons
                'button.clear_chat': 'Xóa lịch sử chat',
                'button.send': 'Gửi tin nhắn',
                'button.attach': 'Đính kèm file',
                'button.language_selector': 'Chọn ngôn ngữ',
                
                // Messages
                'message.welcome': '👋 Xin chào! Tôi là AI Assistant của FOIS ICT PRO',
                'welcome.start_message': 'Hãy bắt đầu bằng cách hỏi tôi bất cứ điều gì! 😊',
                'message.error_connection': 'Không thể kết nối đến server. Vui lòng thử lại.',
                'message.error_general': 'Có lỗi xảy ra, vui lòng thử lại.',
                'message.clear_confirm': 'Bạn có chắc muốn xóa toàn bộ lịch sử chat?',
                'message.clear_success': 'Đã xóa lịch sử chat',
                'message.clear_error': 'Không thể xóa lịch sử chat',
                
                // CV Upload
                'cv.modal_title': 'Upload CV để phân tích',
                'cv.drag_drop': 'Kéo thả file CV vào đây hoặc',
                'cv.select_file': 'Chọn file',
                'cv.supported_formats': 'Hỗ trợ: PDF, DOCX, TXT, JPG, PNG (tối đa 10MB)',
                'cv.uploading': 'Đang upload...',
                'cv.analyzing': 'Đang phân tích CV...',
                'cv.complete': 'Hoàn tất!',
                'cv.success': 'CV đã được phân tích thành công!',
                'cv.error_format': 'Định dạng file không hỗ trợ. Chỉ chấp nhận: PDF, DOCX, TXT, JPG, PNG',
                'cv.error_size': 'File quá lớn. Tối đa 10MB',
                'cv.error_select': 'Vui lòng chọn file CV',
                'cv.error_upload': 'Không thể upload CV. Vui lòng thử lại.',
                'cv.analysis_success': 'Phân tích CV thành công!',
                'cv.candidate': 'Ứng viên',
                'cv.skills': 'Kỹ năng',
                'cv.matching_jobs': 'Công việc phù hợp',
                'cv.suggestions': 'Gợi ý',
                'cv.match_percent': 'phù hợp',
                'cv.analysis_complete_message': 'CV của bạn đã được phân tích thành công!',
                'cv.jobs_found': 'Tìm thấy {count} công việc phù hợp:',
                'cv.job_at': 'tại',
                'cv.match_score': 'Độ phù hợp:',
                'cv.more_jobs': '... và {count} công việc khác.',
                'cv.no_perfect_match': 'Hiện tại chưa có vị trí phù hợp hoàn toàn. Hãy cập nhật thêm kỹ năng!',
                'cv.ask_more': 'Bạn có thể hỏi tôi thêm về bất kỳ vị trí nào!',
                
                // PDF Upload
                'pdf.processing': 'Đang xử lý file PDF',
                'pdf.success': 'Đã đọc thành công file PDF',
                'pdf.uploaded': 'Đã upload file',
                'pdf.error_format': 'Chỉ hỗ trợ file PDF. Vui lòng chọn file PDF.',
                'pdf.error_size': 'File quá lớn. Vui lòng chọn file PDF nhỏ hơn 10MB.',
                'pdf.error_upload': 'Không thể upload file. Vui lòng thử lại.',
                
                // Notifications
                'notification.success': 'Thành công',
                'notification.error': 'Lỗi',
                'notification.info': 'Thông tin',
                
                // Theme
                'theme.light': 'Chuyển sang giao diện sáng',
                'theme.dark': 'Chuyển sang giao diện tối',
                
                // Quick Action Messages
                'quick.company': 'FOIS ICT PRO là gì?',
                'quick.jobs': 'Tôi muốn tìm job Python Developer',
                'quick.salary': 'Mức lương developer bao nhiêu?',
                'quick.guide': 'Hướng dẫn sử dụng hệ thống',

                // Footer
                'footer.powered_by': 'Được hỗ trợ bởi FOIS ICT PRO AI Technology'
            },

            // English
            'en': {
                // Header
                'header.title': 'FOIS ICT PRO AI Assistant',
                'header.status': 'Online',
                'header.theme_toggle': 'Toggle theme',
                
                // Sidebar
                'sidebar.company_experience': 'years of experience',
                'sidebar.location': 'Japan & Vietnam',
                'sidebar.mission': 'Creating optimal technology solutions for businesses...',
                'sidebar.clear_history': 'Clear history',
                'sidebar.language': 'Language',
                
                // Quick Actions
                'actions.company': 'Company Info',
                'actions.jobs': 'Find Jobs',
                'actions.salary': 'Salary Info',
                'actions.guide': 'User Guide',
                'actions.upload_cv': 'Upload CV',
                
                // Chat Interface
                'chat.placeholder': 'Type your message...',
                'chat.send': 'Send',
                'chat.attach': 'Attach file',
                'chat.typing': 'Typing...',
                'chat.suggestions_title': 'Suggested questions',

                // Buttons
                'button.clear_chat': 'Clear chat history',
                'button.send': 'Send message',
                'button.attach': 'Attach file',
                'button.language_selector': 'Select language',
                
                // Messages
                'message.welcome': '👋 Hello! I\'m AI Assistant of FOIS ICT PRO',
                'welcome.start_message': 'Feel free to ask me anything! 😊',
                'message.error_connection': 'Unable to connect to server. Please try again.',
                'message.error_general': 'An error occurred, please try again.',
                'message.clear_confirm': 'Are you sure you want to clear all chat history?',
                'message.clear_success': 'Chat history cleared',
                'message.clear_error': 'Unable to clear chat history',
                
                // CV Upload
                'cv.modal_title': 'Upload CV for Analysis',
                'cv.drag_drop': 'Drag and drop your CV here or',
                'cv.select_file': 'Select file',
                'cv.supported_formats': 'Supported: PDF, DOCX, TXT, JPG, PNG (max 10MB)',
                'cv.uploading': 'Uploading...',
                'cv.analyzing': 'Analyzing CV...',
                'cv.complete': 'Complete!',
                'cv.success': 'CV analyzed successfully!',
                'cv.error_format': 'Unsupported file format. Only accept: PDF, DOCX, TXT, JPG, PNG',
                'cv.error_size': 'File too large. Maximum 10MB',
                'cv.error_select': 'Please select a CV file',
                'cv.error_upload': 'Unable to upload CV. Please try again.',
                'cv.analysis_success': 'CV Analysis Successful!',
                'cv.candidate': 'Candidate',
                'cv.skills': 'Skills',
                'cv.matching_jobs': 'Matching Jobs',
                'cv.suggestions': 'Suggestions',
                'cv.match_percent': 'match',
                'cv.analysis_complete_message': 'Your CV has been analyzed successfully!',
                'cv.jobs_found': 'Found {count} matching jobs:',
                'cv.job_at': 'at',
                'cv.match_score': 'Match score:',
                'cv.more_jobs': '... and {count} more jobs.',
                'cv.no_perfect_match': 'Currently no perfect matches found. Consider updating your skills!',
                'cv.ask_more': 'Feel free to ask me about any position!',
                
                // PDF Upload
                'pdf.processing': 'Processing PDF file',
                'pdf.success': 'Successfully read PDF file',
                'pdf.uploaded': 'File uploaded',
                'pdf.error_format': 'Only PDF files supported. Please select a PDF file.',
                'pdf.error_size': 'File too large. Please select a PDF file smaller than 10MB.',
                'pdf.error_upload': 'Unable to upload file. Please try again.',
                
                // Notifications
                'notification.success': 'Success',
                'notification.error': 'Error',
                'notification.info': 'Information',
                
                // Theme
                'theme.light': 'Switch to light theme',
                'theme.dark': 'Switch to dark theme',
                
                // Quick Action Messages
                'quick.company': 'What is FOIS ICT PRO?',
                'quick.jobs': 'I want to find a Python Developer job',
                'quick.salary': 'What is the developer salary range?',
                'quick.guide': 'System user guide',

                // Footer
                'footer.powered_by': 'Powered by <strong>FOIS ICT PRO</strong> AI Technology'
            },

            // Japanese
            'ja': {
                // Header
                'header.title': 'FOIS ICT PRO AI アシスタント',
                'header.status': 'オンライン',
                'header.theme_toggle': 'テーマ切り替え',
                
                // Sidebar
                'sidebar.company_experience': '年の経験',
                'sidebar.location': '日本・ベトナム',
                'sidebar.mission': '企業向け最適なテクノロジーソリューションの創造...',
                'sidebar.clear_history': '履歴削除',
                'sidebar.language': '言語',
                
                // Quick Actions
                'actions.company': '会社情報',
                'actions.jobs': '求人検索',
                'actions.salary': '給与情報',
                'actions.guide': '使用ガイド',
                'actions.upload_cv': 'CV アップロード',
                
                // Chat Interface
                'chat.placeholder': 'メッセージを入力してください...',
                'chat.send': '送信',
                'chat.attach': 'ファイル添付',
                'chat.typing': '入力中...',
                'chat.suggestions_title': '質問の提案',

                // Buttons
                'button.clear_chat': 'チャット履歴削除',
                'button.send': 'メッセージ送信',
                'button.attach': 'ファイル添付',
                'button.language_selector': '言語選択',
                
                // Messages
                'message.welcome': '👋 こんにちは！私はFOIS GROUPのAIアシスタントです',
                'welcome.start_message': '何でもお気軽にお聞きください！😊',
                'message.error_connection': 'サーバーに接続できません。もう一度お試しください。',
                'message.error_general': 'エラーが発生しました。もう一度お試しください。',
                'message.clear_confirm': 'チャット履歴をすべて削除してもよろしいですか？',
                'message.clear_success': 'チャット履歴を削除しました',
                'message.clear_error': 'チャット履歴を削除できません',
                
                // CV Upload
                'cv.modal_title': 'CV分析のためのアップロード',
                'cv.drag_drop': 'CVをここにドラッグ＆ドロップするか',
                'cv.select_file': 'ファイル選択',
                'cv.supported_formats': '対応形式: PDF, DOCX, TXT, JPG, PNG (最大10MB)',
                'cv.uploading': 'アップロード中...',
                'cv.analyzing': 'CV分析中...',
                'cv.complete': '完了！',
                'cv.success': 'CVの分析が完了しました！',
                'cv.error_format': 'サポートされていないファイル形式です。PDF, DOCX, TXT, JPG, PNGのみ対応',
                'cv.error_size': 'ファイルが大きすぎます。最大10MB',
                'cv.error_select': 'CVファイルを選択してください',
                'cv.error_upload': 'CVをアップロードできません。もう一度お試しください。',
                'cv.analysis_success': 'CV分析成功！',
                'cv.candidate': '候補者',
                'cv.skills': 'スキル',
                'cv.matching_jobs': 'マッチング求人',
                'cv.suggestions': '提案',
                'cv.match_percent': 'マッチ',
                'cv.analysis_complete_message': 'CVの分析が正常に完了しました！',
                'cv.jobs_found': '{count}件のマッチング求人が見つかりました:',
                'cv.job_at': 'の',
                'cv.match_score': 'マッチ度:',
                'cv.more_jobs': '... その他{count}件の求人があります。',
                'cv.no_perfect_match': '現在、完全にマッチする求人はありません。スキルを更新することをお勧めします！',
                'cv.ask_more': 'どのポジションについてもお気軽にお尋ねください！',
                
                // PDF Upload
                'pdf.processing': 'PDFファイル処理中',
                'pdf.success': 'PDFファイルの読み取りが完了しました',
                'pdf.uploaded': 'ファイルアップロード完了',
                'pdf.error_format': 'PDFファイルのみサポートしています。PDFファイルを選択してください。',
                'pdf.error_size': 'ファイルが大きすぎます。10MB以下のPDFファイルを選択してください。',
                'pdf.error_upload': 'ファイルをアップロードできません。もう一度お試しください。',
                
                // Notifications
                'notification.success': '成功',
                'notification.error': 'エラー',
                'notification.info': '情報',
                
                // Theme
                'theme.light': 'ライトテーマに切り替え',
                'theme.dark': 'ダークテーマに切り替え',
                
                // Quick Action Messages
                'quick.company': 'FOIS GROUPとは何ですか？',
                'quick.jobs': 'Python開発者の求人を探しています',
                'quick.salary': '開発者の給与はいくらですか？',
                'quick.guide': 'システム使用ガイド',

                // Footer
                'footer.powered_by': '<strong>FOIS ICT PRO</strong> AI テクノロジーによって提供'
            }
        };
    }

    // Get translation for a key
    t(key, params = {}) {
        const translation = this.translations[this.currentLanguage]?.[key] || 
                          this.translations['vi'][key] || 
                          key;
        
        // Replace parameters in translation
        return Object.keys(params).reduce((str, param) => {
            return str.replace(`{{${param}}}`, params[param]);
        }, translation);
    }

    // Set current language
    setLanguage(lang) {
        if (this.translations[lang]) {
            this.currentLanguage = lang;
            localStorage.setItem('chatbot-language', lang);
            console.log(`🌍 Language set to: ${lang}`);

            // Update URL parameter
            this.updateURL(lang);

            this.updateUI();
            return true;
        }
        return false;
    }

    // Update URL with language parameter
    updateURL(lang) {
        const url = new URL(window.location);

        // Map to full locale codes for URL
        const localeMap = {
            'vi': 'vi_VN',
            'en': 'en_US',
            'ja': 'ja_JP'
        };

        const locale = localeMap[lang] || lang;
        url.searchParams.set('language', locale);

        // Update URL without page reload
        window.history.replaceState({}, '', url);
    }

    // Get current language
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // Get available languages
    getAvailableLanguages() {
        return [
            { code: 'vi', name: 'Tiếng Việt (vi_VN)', flag: '🇻🇳', locale: 'vi_VN' },
            { code: 'en', name: 'English (en_US)', flag: '🇺🇸', locale: 'en_US' },
            { code: 'ja', name: '日本語 (ja_JP)', flag: '🇯🇵', locale: 'ja_JP' }
        ];
    }

    // Update UI with current language
    updateUI() {
        // Update all elements with data-i18n attribute
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);
            
            if (element.tagName === 'INPUT' && element.type === 'text') {
                element.placeholder = translation;
            } else {
                element.textContent = translation;
            }
        });

        // Update elements with data-i18n-title attribute
        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });

        // Update elements with data-i18n-html attribute (for HTML content)
        document.querySelectorAll('[data-i18n-html]').forEach(element => {
            const key = element.getAttribute('data-i18n-html');
            element.innerHTML = this.t(key);
        });

        // Update language selector display
        this.updateLanguageSelector();

        // Trigger custom event for components that need to update
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage }
        }));
    }

    // Update language selector display and title
    updateLanguageSelector() {
        const langCurrent = document.getElementById('langCurrent');
        if (langCurrent) {
            // Update title
            const titleKey = langCurrent.getAttribute('data-i18n-title');
            if (titleKey) {
                langCurrent.title = this.t(titleKey);
                console.log(`🌍 Updated language selector title: "${langCurrent.title}"`);
            }

            // Update current language display
            const languages = this.getAvailableLanguages();
            const currentLang = languages.find(lang => lang.code === this.currentLanguage);

            if (currentLang) {
                const flagSpan = langCurrent.querySelector('.lang-flag');
                const nameSpan = langCurrent.querySelector('.lang-name');

                if (flagSpan) flagSpan.textContent = currentLang.flag;
                if (nameSpan) nameSpan.textContent = currentLang.name;

                console.log(`🌍 Updated language selector display: ${currentLang.name}`);
            }

            // Update active state in dropdown options
            const langOptions = document.querySelectorAll('.lang-option');
            langOptions.forEach(option => {
                const langCode = option.getAttribute('data-lang');
                if (langCode === this.currentLanguage) {
                    option.classList.add('active');
                } else {
                    option.classList.remove('active');
                }
            });
        }
    }

    // Initialize i18n
    init() {
        this.updateUI();
        this.createLanguageSelector();
    }

    // Create language selector
    createLanguageSelector() {
        const languages = this.getAvailableLanguages();
        const currentLang = languages.find(lang => lang.code === this.currentLanguage);
        
        // Find or create language selector container
        let langSelector = document.getElementById('languageSelector');
        if (!langSelector) {
            langSelector = document.createElement('div');
            langSelector.id = 'languageSelector';
            langSelector.className = 'language-selector';
            
            // Insert into sidebar footer
            const sidebarFooter = document.querySelector('.sidebar-footer');
            if (sidebarFooter) {
                sidebarFooter.insertBefore(langSelector, sidebarFooter.firstChild);
            }
        }

        langSelector.innerHTML = `
            <div class="lang-dropdown">
                <button class="lang-current" id="langCurrent" data-i18n-title="button.language_selector" title="${this.t('button.language_selector')}">
                    <span class="lang-flag">${currentLang.flag}</span>
                    <span class="lang-name">${currentLang.name}</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="lang-options" id="langOptions">
                    ${languages.map(lang => `
                        <div class="lang-option ${lang.code === this.currentLanguage ? 'active' : ''}"
                             data-lang="${lang.code}"
                             title="URL: /modern?language=${lang.locale}">
                            <span class="lang-flag">${lang.flag}</span>
                            <span class="lang-name">${lang.name}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        // Add event listeners
        const langCurrent = langSelector.querySelector('#langCurrent');
        const langOptions = langSelector.querySelector('#langOptions');

        langCurrent.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Check if there's enough space below or above
            const rect = langCurrent.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const spaceBelow = viewportHeight - rect.bottom;
            const spaceAbove = rect.top;
            const dropdownHeight = 150; // Estimated dropdown height

            // Reset classes
            langOptions.classList.remove('dropdown-up', 'dropdown-down');

            // Determine direction
            if (spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove) {
                // Open downward
                langOptions.classList.add('dropdown-down');
            } else {
                // Open upward
                langOptions.classList.add('dropdown-up');
            }

            langOptions.classList.toggle('show');

            // Ensure dropdown is visible after opening
            if (langOptions.classList.contains('show')) {
                setTimeout(() => {
                    const optionsRect = langOptions.getBoundingClientRect();
                    if (optionsRect.bottom > viewportHeight || optionsRect.top < 0) {
                        // If still not visible, force upward direction
                        langOptions.classList.remove('dropdown-down');
                        langOptions.classList.add('dropdown-up');
                    }
                }, 50);
            }
        });

        langSelector.querySelectorAll('.lang-option').forEach(option => {
            option.addEventListener('click', () => {
                const langCode = option.getAttribute('data-lang');
                this.setLanguage(langCode);
                langOptions.classList.remove('show');
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!langSelector.contains(e.target)) {
                langOptions.classList.remove('show');
            }
        });

        // Update title after creation
        setTimeout(() => {
            this.updateLanguageSelector();
        }, 0);
    }
}

// Export for use in other files
window.I18n = I18n;
